from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class ProductTemplate(models.Model):
    _inherit ="product.template"

    old_reference = fields.Char(string='Ancienne référence')

    # Configuration des dimensions
    dimension_type = fields.Selection([
        ('none', 'Aucune dimension'),
        ('length_only', 'Longueur seulement'),
        ('length_width', 'Longueur et Largeur')
    ], string="Type de dimensions", default='length_width',
       help="Définit quelles dimensions sont requises pour ce produit")

    # Dimensions du produit
    longueur = fields.Float(string='Longueur (mm)', default=1000.0,
                           help="Longueur standard du produit en millimètres")
    largeur = fields.Float(string='Largeur (mm)', default=1000.0,
                          help="Largeur standard du produit en millimètres")

    # Dimensions par défaut (pour compatibilité)
    default_length = fields.Float(string='Longueur par défaut (mm)',
                                 related='longueur', store=True, readonly=False)
    default_width = fields.Float(string='Largeur par défaut (mm)',
                                related='largeur', store=True, readonly=False)

    # Configuration du pricing
    pricing_type = fields.Selection([
        ('piece', 'Pièce'),
        ('metre', 'Mètre linéaire'),
        ('surface', 'Surface')
    ], string="Type de tarification", default='piece')

    # Prix selon le type
    price_per_meter = fields.Float(string="Prix au mètre linéaire", default=0.0)
    price_per_m2 = fields.Float(string="Prix au m²", default=0.0)
    montant_par_pli = fields.Float(string="Montant par pli (DA)", default=0.0)

    # Contraintes de dimensions
    min_length = fields.Float(string='Longueur minimale (mm)', default=0.0)
    max_length = fields.Float(string='Longueur maximale (mm)', default=0.0)
    min_width = fields.Float(string='Largeur minimale (mm)', default=0.0)
    max_width = fields.Float(string='Largeur maximale (mm)', default=0.0)

    @api.onchange('dimension_type')
    def _onchange_dimension_type(self):
        """Adapter le type de tarification selon les dimensions"""
        if self.dimension_type == 'none':
            self.pricing_type = 'piece'
            # Réinitialiser les dimensions pour les produits sans dimensions
            self.longueur = 0.0
            self.largeur = 0.0
        elif self.dimension_type == 'length_only':
            self.pricing_type = 'metre'
            # Garder la longueur, réinitialiser la largeur
            if self.longueur == 0.0:
                self.longueur = 1000.0
            self.largeur = 1.0
        elif self.dimension_type == 'length_width':
            self.pricing_type = 'surface'
            # Initialiser les deux dimensions si elles sont à 0
            if self.longueur == 0.0:
                self.longueur = 1000.0
            if self.largeur == 0.0:
                self.largeur = 1000.0

    @api.constrains('dimension_type', 'longueur', 'largeur')
    def _check_product_dimensions(self):
        """Valider les dimensions selon le type de produit"""
        for record in self:
            if record.dimension_type == 'length_only':
                if record.longueur <= 0:
                    raise ValidationError(_("La longueur doit être supérieure à 0 pour un produit linéaire."))
            elif record.dimension_type == 'length_width':
                if record.longueur <= 0:
                    raise ValidationError(_("La longueur doit être supérieure à 0 pour un produit surfacique."))
                if record.largeur <= 0:
                    raise ValidationError(_("La largeur doit être supérieure à 0 pour un produit surfacique."))
            # Pour dimension_type == 'none', pas de validation des dimensions



class ProductProduct(models.Model):
    _inherit ="product.product"

    # Champs liés au template
    dimension_type = fields.Selection(related="product_tmpl_id.dimension_type")
    pricing_type = fields.Selection(related="product_tmpl_id.pricing_type")
    longueur = fields.Float(related="product_tmpl_id.longueur")
    largeur = fields.Float(related="product_tmpl_id.largeur")
    default_length = fields.Float(related="product_tmpl_id.default_length")
    default_width = fields.Float(related="product_tmpl_id.default_width")
    price_per_meter = fields.Float(related="product_tmpl_id.price_per_meter")
    price_per_m2 = fields.Float(related="product_tmpl_id.price_per_m2")