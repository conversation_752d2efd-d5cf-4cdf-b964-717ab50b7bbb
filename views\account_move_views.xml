<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_inherit" model="ir.ui.view">
        <field name="name">account.move.form.inherit.dimensions</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <!--            <xpath expr="//field[@name='invoice_line_ids']/tree//field[@name='product_id']" position="after">-->
            <!--                <field name="longueur" optional="show" invisible="not product_id"/>-->
            <!--                <field name="largeur" optional="show" invisible="not product_id"/>-->
            <!--                <field name="surface" optional="show" readonly="1"/>-->
            <!--            </xpath>-->

            <page id="invoice_tab" name="invoice_tab" position="replace"
            >
                <page id="invoice_tab" name="invoice_tab" string="Lignes de facture" invisible="move_type == 'entry'">
                    <field name="invoice_line_ids" widget="section_and_note_one2many" mode="tree,kanban"
                           context="{                                            'default_move_type': context.get('default_move_type'),                                            'journal_id': journal_id,                                            'default_partner_id': commercial_partner_id,                                            'default_currency_id': currency_id or company_currency_id,                                            'default_display_type': 'product',                                            'quick_encoding_vals': quick_encoding_vals,                                        }"
                           readonly="state != 'draft'" on_change="1" field_id="invoice_line_ids_0">
                        <tree editable="bottom" string="Écritures comptables" default_order="sequence, id">
                            <control>
                                <create name="add_line_control" string="Ajouter une ligne"/>
                                <create name="add_section_control" string="Ajouter une section"
                                        context="{'default_display_type': 'line_section'}"/>
                                <create name="add_note_control" string="Ajouter une note"
                                        context="{'default_display_type': 'line_note'}"/>
                            </control>

                            <!-- Displayed fields -->
                            <field name="dimension_type" invisible="1"/>
                            <field name="sequence" widget="handle"/>
                            <field name="product_id" optional="show" widget="many2one_barcode"
                                   domain="                                                     context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')                                                     and [('sale_ok', '=', True)]                                                     or [('purchase_ok', '=', True)]                                                "
                                   on_change="1" can_create="True" can_write="True"/>
                            <field name="quantity" optional="show" on_change="1" string="Nombre"/>
                            <field name="longueur" optional="show" invisible="dimension_type == 'none'"
                                   required="dimension_type in ['length_only', 'length_width']"/>
                            <field name="largeur" optional="show" invisible="dimension_type != 'length_width'"
                                   required="dimension_type == 'length_width'"/>
                            <field name="surface" optional="show" invisible="dimension_type != 'length_width'"
                                   readonly="1"/>
                            <field name="name" widget="section_and_note_text" optional="show"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="product_uom_id" string="UdM" optional="show" on_change="1" can_create="True"
                                   can_write="True"/>
                            <!-- /l10n_in_edi.test_edi_json -->
                            <!-- required for @api.onchange('product_id') -->
                            <field name="product_uom_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="price_unit" string="Prix" on_change="1"/>
                            <field name="discount" string="Rem.%" optional="hide" on_change="1"/>
                            <field name="tax_ids" widget="many2many_tags"
                                   domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                   context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain, 'active_test': True}"
                                   options="{'no_create': True}" optional="show" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="price_subtotal" string="Hors taxes" on_change="1"/>
                            <field name="price_total" string="Toutes taxes comprises"
                                   column_invisible="parent.tax_calculation_rounding_method == 'round_globally'"
                                   optional="hide" on_change="1"/>

                            <!-- Others fields -->
                            <field name="partner_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="currency_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="company_id" column_invisible="True" on_change="1"/>
                            <field name="company_currency_id" column_invisible="True"/>
                            <field name="display_type" force_save="1" column_invisible="True" on_change="1"/>
                        </tree>
                        <kanban class="o_kanban_mobile">
                            <!-- Displayed fields -->
                            <field name="name"/>
                            <field name="product_id" on_change="1"/>
                            <field name="price_subtotal" string="Hors taxes" on_change="1"/>
                            <field name="price_total" string="Toutes taxes comprises" optional="hide" on_change="1"/>
                            <field name="quantity" on_change="1" string="Nombre"/>
                            <field name="product_uom_category_id"/>
                            <field name="product_uom_id" on_change="1"/>
                            <field name="price_unit" on_change="1"/>
                            <templates>
                                <t t-name="kanban-box">
                                    <div t-attf-class="oe_kanban_card oe_kanban_global_click ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                        <t t-if="!['line_note', 'line_section'].includes(record.display_type.raw_value)">
                                            <div class="row g-0">
                                                <div class="col-2 pe-3">
                                                    <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)"
                                                         t-att-title="record.product_id.value"
                                                         t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                </div>
                                                <div class="col-10">
                                                    <div class="row">
                                                        <div class="col">
                                                            <strong t-out="record.product_id.value"/>
                                                        </div>
                                                        <div class="col-auto">
                                                            <strong class="float-end text-end">
                                                                <t t-out="record.price_subtotal.value"/>
                                                                <t t-out="record.price_total.value"
                                                                   t-if="tax_calculation_rounding_method == 'round_per_line'"/>
                                                            </strong>
                                                        </div>
                                                    </div>
                                                    <div class="text-muted">
                                                        Quantité :
                                                        <t t-out="record.quantity.value"/>
                                                        <t t-out="record.product_uom_id.value"/>
                                                    </div>
                                                    <div class="text-muted">
                                                        Prix unitaire :
                                                        <t t-out="record.price_unit.value"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                        <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                            <div class="row">
                                                <div class="col-12">
                                                    <t t-out="record.name.value"/>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </t>
                            </templates>

                            <!-- Others fields -->
                            <field name="tax_calculation_rounding_method"/>
                            <field name="currency_id" on_change="1"/>
                            <field name="company_currency_id"/>
                            <field name="display_type" force_save="1" on_change="1"/>
                        </kanban>

                        <!-- Form view to cover mobile use -->
                        <form>
                            <sheet>
                                <field name="tax_calculation_rounding_method" invisible="1"/>
                                <field name="display_type" invisible="1" on_change="1"/>
                                <field name="company_id" invisible="1" on_change="1"/>
                                <field name="partner_id" invisible="1" on_change="1" can_create="True"
                                       can_write="True"/>
                                <group>
                                    <field name="product_id" widget="many2one_barcode" on_change="1" can_create="True"
                                           can_write="True"/>
                                    <field name="quantity" on_change="1" string="Nombre"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="product_uom_id" on_change="1" can_create="True" can_write="True"/>
                                    <field name="price_unit" on_change="1"/>
                                    <field name="discount" string="Rem.%" on_change="1"/>
                                </group>
                                <group>
                                    <field name="account_id"
                                           domain="[('company_id', 'parent_of', company_id), ('deprecated', '=', False)]"
                                           options="{'no_create': True}"
                                           context="{'partner_id': partner_id, 'move_type': parent.move_type}"
                                           on_change="1"
                                           can_create="True" can_write="True"/>
                                    <field name="tax_ids" widget="many2many_tags" on_change="1" can_create="True"
                                           can_write="True"/>
                                </group>
                                <label for="name" string="Description"
                                       invisible="display_type in ('line_note', 'line_section')"/>
                                <label for="name" string="Section" invisible="display_type != 'line_section'"/>
                                <label for="name" string="Note" invisible="display_type != 'line_note'"/>
                                <field name="name" widget="text"/>
                                <group>
                                    <field name="price_subtotal" string="Hors taxes" on_change="1"/>
                                    <field name="price_total" string="Toutes taxes comprises"
                                           invisible="tax_calculation_rounding_method == 'round_globally'"
                                           optional="hide"
                                           on_change="1"/>
                                </group>
                            </sheet>
                        </form>
                    </field>
                    <group col="12" class="oe_invoice_lines_tab">
                        <group colspan="8">
                            <field name="narration" placeholder="Conditions générales" colspan="2" nolabel="1"
                                   field_id="narration_0"/>
                        </group>
                        <!-- Totals (only invoices / receipts) -->
                        <group colspan="4">
                            <group class="oe_subtotal_footer"
                                   invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') or payment_state == 'invoicing_legacy'">

                                <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2"
                                       readonly="state != 'draft' or (move_type not in ('in_invoice', 'in_refund', 'in_receipt') and not quick_edit_mode)"
                                       on_change="1" field_id="tax_totals_0"/>

                                <field name="invoice_payments_widget" colspan="2" nolabel="1" widget="payment"
                                       field_id="invoice_payments_widget_0"/>
                                <field name="amount_residual" class="oe_subtotal_footer_separator"
                                       invisible="state == 'draft'" on_change="1" field_id="amount_residual_0"/>
                            </group>
                            <field name="invoice_outstanding_credits_debits_widget"
                                   class="oe_invoice_outstanding_credits_debits" colspan="2" nolabel="1"
                                   widget="payment"
                                   invisible="state != 'posted'"
                                   field_id="invoice_outstanding_credits_debits_widget_0"/>
                        </group>
                    </group>
                </page>
            </page>

        </field>
    </record>
</odoo> 