<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="purchase_order_view_form_inherit" model="ir.ui.view">
            <field name="name">purchase.order.form.inherit</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='order_line']//tree" position="replace">
                    <tree string="Lignes de bon de commande" editable="bottom">
                        <control>
                            <create name="add_product_control" string="Ajouter un produit"/>
                            <create name="add_section_control" string="Ajouter une section"
                                    context="{'default_display_type': 'line_section'}"/>
                            <create name="add_note_control" string="Ajouter une note"
                                    context="{'default_display_type': 'line_note'}"/>
                            <button name="action_add_from_catalog" string="Catalogue" type="object"
                                    class="px-4 btn-link" context="{'order_id': parent.id}"/>
                        </control>
                        <field name="tax_calculation_rounding_method" column_invisible="True"/>
                        <field name="display_type" column_invisible="True"/>
                        <field name="company_id" column_invisible="True" on_change="1"/>
                        <field name="currency_id" column_invisible="True"/>
                        <field name="state" column_invisible="True"/>
                        <field name="product_type" column_invisible="True"/>
                        <field name="product_uom" column_invisible="True" on_change="1" can_create="True"
                               can_write="True"/>
                        <field name="product_uom_category_id" column_invisible="True"/>
                        <field name="invoice_lines" column_invisible="True" on_change="1"/>
                        <field name="sequence" widget="handle"/>
                        <field name="product_id" readonly="state in ('purchase', 'to approve', 'done', 'cancel')"
                               required="not display_type" width="35%"
                               context="{'partner_id': parent.partner_id, 'quantity': product_qty, 'company_id': parent.company_id, 'use_partner_name': False}"
                               force_save="1" domain="[('purchase_ok', '=', True)]" on_change="1" can_create="True"
                               can_write="True"/>
                        <field name="name" widget="section_and_note_text"/>
                        <field name="date_planned" optional="hide" required="not display_type" force_save="1"
                               on_change="1"/>
                        <field name="move_dest_ids" column_invisible="True" on_change="1" can_create="True"
                               can_write="True"/>
                        <field name="propagate_cancel" optional="hide"/>
                        <field name="product_qty" on_change="1"/>
                        <!-- Champs de dimensions pour la vue formulaire -->
                        <field name="dimension_type" invisible="1"/>
                        <field name="longueur"
                               string="Longueur (mm)"
                               invisible="dimension_type == 'none'"
                               required="dimension_type in ['length_only', 'length_width']"/>
                        <field name="largeur"
                               string="Largeur (mm)"
                               invisible="dimension_type != 'length_width'"
                               required="dimension_type == 'length_width'"/>
                        <field name="surface"
                               string="Surface (m²)"
                               invisible="dimension_type != 'length_width'"
                               readonly="1"/>
                        <field name="total_length"
                               string="Longueur totale (m)"
                               invisible="dimension_type == 'none'"
                               readonly="1"/>
                        <field name="forecasted_issue" column_invisible="True"/>
                        <button type="object" name="action_product_forecast_report" title="Rapport de prévision"
                                icon="fa-area-chart"
                                invisible="not id or not forecasted_issue or product_type != 'product'"
                                class="text-danger"/>
                        <button type="object" name="action_product_forecast_report" title="Rapport de prévision"
                                icon="fa-area-chart"
                                invisible="not id or forecasted_issue or product_type != 'product'"/>
                        <field name="qty_received_manual" column_invisible="True" on_change="1"/>
                        <field name="qty_received_method" column_invisible="True" on_change="1"/>
                        <field name="qty_received" string="Reçu"
                               column_invisible="parent.state not in ('purchase', 'done')"
                               readonly="product_type in ('consu', 'product')" optional="show" on_change="1"/>
                        <field name="qty_invoiced" string="Facturé"
                               column_invisible="parent.state not in ('purchase', 'done')" optional="show"/>
                        <field name="price_unit" readonly="qty_invoiced != 0" on_change="1"/>
                        <button name="action_purchase_history" type="object" icon="fa-history"
                                title="Historique des achats" invisible="not id"/>
                        <field name="taxes_id" widget="many2many_tags"
                               domain="[('type_tax_use', '=', 'purchase'), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id), ('active', '=', True)]"
                               context="{'default_type_tax_use': 'purchase', 'search_view_ref': 'account.account_tax_view_search'}"
                               options="{'no_create': True}" optional="show" on_change="1" can_create="True"
                               can_write="True"/>
                        <field name="discount" string="Rem.%" readonly="qty_invoiced != 0" optional="hide"
                               on_change="1"/>
                        <field name="price_subtotal" string="Hors taxes" on_change="1"/>
                        <field name="price_total" string="Toutes taxes comprises"
                               column_invisible="parent.tax_calculation_rounding_method == 'round_globally'"
                               optional="hide" on_change="1"/>
                    </tree>
                </xpath>
            </field>
        </record>
    </data>
</odoo>