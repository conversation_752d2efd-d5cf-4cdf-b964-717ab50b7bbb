from odoo import api,models,fields,_
from odoo.tools.float_utils import float_is_zero
from odoo.exceptions import UserError, ValidationError
from odoo.tools import format_amount, format_date, formatLang, groupby


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    @api.model
    def _prepare_account_move_line(self):
        res = super()._prepare_account_move_line()
        res.update({
            'longueur': self.longueur,
            'largeur': self.largeur,
            'surface': self.surface,
            'surface_unit': self.surface_unit,
            'total_length': self.total_length,
            'dimension_type': self.dimension_type,
            'pricing_type': self.pricing_type,
        })
        return res


    # Dimensions selon le type de produit
    longueur = fields.Float(string='Longueur (mm)', store=True, default=0.00)
    largeur = fields.Float(string='Largeur (mm)', store=True, default=0.00)
    surface = fields.Float(string='Surface (m²)', store=True, default=0.00, compute='_compute_surface')
    surface_unit = fields.Float(string='Surface unitaire (m²)', store=True, compute='_compute_surface')
    total_length = fields.Float(string='Longueur totale (m)', store=True, compute='_compute_surface')
    nombre_plis = fields.Integer(string="Nombre de plis", default=1)
    price_subtotal = fields.Monetary(compute="_compute_amount", store=True)
    price_tax = fields.Monetary(compute="_compute_amount", store=True)
    price_total = fields.Monetary(compute="_compute_amount", store=True)
    dimension_type = fields.Selection([
        ('none', 'Aucune dimension'),
        ('length_only', 'Longueur seulement'),
        ('length_width', 'Longueur et Largeur')
    ], string="Type de dimensions", default='length_width')

    pricing_type = fields.Selection([
        ('piece', 'Pièce'),
        ('metre', 'Mètre linéaire'),
        ('surface', 'Surface')
    ], string="Type de tarification", default='piece')

    def _convert_to_tax_base_line_dict(self):
        """ Calcul du montant de base pour les taxes en tenant compte de la surface """
        result = super()._convert_to_tax_base_line_dict()

        if self.longueur and self.largeur:
            # La quantité effective est la surface totale
            result['quantity'] = self.surface

            # Ajuster le prix unitaire si nécessaire
            if hasattr(self.product_id, 'surface_price'):
                result['price_unit'] = self.product_id.surface_price

        return result

    @api.depends('longueur', 'largeur', 'product_uom_qty', 'dimension_type')
    def _compute_surface(self):
        for record in self:
            # Calcul selon le type de dimensions
            if record.dimension_type == 'length_width' and record.longueur and record.largeur:
                # Produit avec longueur et largeur
                record.surface_unit = (record.longueur / 1000) * (record.largeur / 1000)
                record.total_length = (record.longueur / 1000) * record.product_uom_qty
                record.surface = record.surface_unit * record.product_uom_qty
            elif record.dimension_type == 'length_only' and record.longueur:
                # Produit avec longueur seulement
                record.total_length = (record.longueur / 1000) * record.product_uom_qty
                record.surface_unit = record.total_length
                record.surface = record.total_length
            else:
                # Produit sans dimensions
                record.surface_unit = 0.0
                record.total_length = 0.0
                record.surface = 0.0

    @api.onchange('product_id')
    def _onchange_product_id_inherit_dimensions(self):
        """Hériter automatiquement les dimensions du produit"""
        if self.product_id:
            # Hériter les dimensions et types du produit
            self.dimension_type = getattr(self.product_id, 'dimension_type', 'none') or 'none'
            self.pricing_type = getattr(self.product_id, 'pricing_type', 'piece') or 'piece'

            # Hériter les dimensions selon le type
            if self.dimension_type == 'length_width':
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
                self.largeur = getattr(self.product_id, 'largeur', 1000.0) or 1000.0
            elif self.dimension_type == 'length_only':
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
                self.largeur = 1
            else:  # dimension_type == 'none'
                self.longueur = 0.0
                self.largeur = 0.0


    @api.onchange('longueur', 'largeur', 'product_uom_qty')
    def _onchange_dimensions(self):
        if self.product_id:
            # Calcul du prix selon le type de tarification
            if self.pricing_type == 'surface' and self.surface_unit > 0:
                self.price_unit = getattr(self.product_id, 'price_per_m2', 0.0) or self.product_id.list_price
            elif self.pricing_type == 'metre' and self.longueur > 0:
                self.price_unit = getattr(self.product_id, 'price_per_meter', 0.0) or self.product_id.list_price

    @api.depends('product_uom_qty', 'discount', 'price_unit', 'taxes_id', 'longueur', 'largeur', 'surface',
                 'dimension_type')
    def _compute_amount(self):
        """Calcul des montants en tenant compte des dimensions"""
        for line in self:
            # Déterminer la quantité effective selon le type de dimensions
            if line.dimension_type == 'length_width' and line.surface > 0:
                # Pour les produits surfaciques, utiliser la surface
                quantity = line.surface
            elif line.dimension_type == 'length_only' and line.total_length > 0:
                # Pour les produits linéaires, utiliser la longueur totale
                quantity = line.total_length
            else:
                # Pour les produits sans dimensions, utiliser la quantité normale
                quantity = line.product_uom_qty

            # Calcul des taxes
            tax_results = self.env['account.tax'].with_company(line.company_id)._compute_taxes([
                line._convert_to_tax_base_line_dict()
            ])
            totals = list(tax_results['totals'].values())[0]
            amount_untaxed = quantity * line.price_unit * (1 - (line.discount or 0.0) / 100.0)
            amount_tax = totals['amount_tax']

            line.update({
                'price_subtotal': amount_untaxed,
                'price_tax': amount_tax,
                'price_total': amount_untaxed + amount_tax,
            })


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    def action_create_invoice(self):
        """Create the invoice associated to the PO.
        """
        precision = self.env['decimal.precision'].precision_get('Product Unit of Measure')

        # 1) Prepare invoice vals and clean-up the section lines
        invoice_vals_list = []
        sequence = 10
        for order in self:
            if order.invoice_status != 'to invoice':
                continue

            order = order.with_company(order.company_id)
            pending_section = None
            # Invoice values.
            invoice_vals = order._prepare_invoice()
            # Invoice line values (keep only necessary sections).
            for line in order.order_line:
                if line.display_type == 'line_section':
                    pending_section = line
                    continue
                if not float_is_zero(line.qty_to_invoice, precision_digits=precision):
                    if pending_section:
                        line_vals = pending_section._prepare_account_move_line()
                        print(f'\n\n line_vals {line_vals} \n\n')
                        line_vals.update({'sequence': sequence})
                        invoice_vals['invoice_line_ids'].append((0, 0, line_vals))
                        sequence += 1
                        pending_section = None
                    line_vals = line._prepare_account_move_line()
                    print(f'\n\n line_vals 2 {line_vals} \n\n')
                    line_vals.update({'sequence': sequence})
                    invoice_vals['invoice_line_ids'].append((0, 0, line_vals))
                    sequence += 1
            invoice_vals_list.append(invoice_vals)
        if not invoice_vals_list:
            raise UserError(_('There is no invoiceable line. If a product has a control policy based on received quantity, please make sure that a quantity has been received.'))

        # 2) group by (company_id, partner_id, currency_id) for batch creation
        new_invoice_vals_list = []
        for grouping_keys, invoices in groupby(invoice_vals_list, key=lambda x: (x.get('company_id'), x.get('partner_id'), x.get('currency_id'))):
            origins = set()
            payment_refs = set()
            refs = set()
            ref_invoice_vals = None
            for invoice_vals in invoices:
                if not ref_invoice_vals:
                    ref_invoice_vals = invoice_vals
                else:
                    ref_invoice_vals['invoice_line_ids'] += invoice_vals['invoice_line_ids']
                origins.add(invoice_vals['invoice_origin'])
                payment_refs.add(invoice_vals['payment_reference'])
                refs.add(invoice_vals['ref'])
            ref_invoice_vals.update({
                'ref': ', '.join(refs)[:2000],
                'invoice_origin': ', '.join(origins),
                'payment_reference': len(payment_refs) == 1 and payment_refs.pop() or False,
            })
            new_invoice_vals_list.append(ref_invoice_vals)
        invoice_vals_list = new_invoice_vals_list

        # 3) Create invoices.
        moves = self.env['account.move']
        AccountMove = self.env['account.move'].with_context(default_move_type='in_invoice')
        for vals in invoice_vals_list:
            moves |= AccountMove.with_company(vals['company_id']).create(vals)

        # 4) Some moves might actually be refunds: convert them if the total amount is negative
        # We do this after the moves have been created since we need taxes, etc. to know if the total
        # is actually negative or not
        moves.filtered(lambda m: m.currency_id.round(m.amount_total) < 0).action_switch_move_type()

        return self.action_view_invoice(moves)