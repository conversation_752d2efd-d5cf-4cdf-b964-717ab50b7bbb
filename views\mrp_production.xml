<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_mrp_production" model="ir.ui.view">
        <field name="name">view.mrp.production</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//label[@for='lot_producing_id']" position="before">
                <label for="length" name="length" invisible="1"/>
                <div class="o_row" name="length_div" invisible="1">
                    <field name="length"/>
                </div>
                <label for="width" name="width" invisible="1"/>
                <div class="o_row" name="width_div" invisible="1">
                    <field name="width"/>
                </div>
                <label for="area" name="area" invisible="1"/>
                <div class="o_row" name="area_div" invisible="1">
                    <field name="area"/>
                </div>
                <label for="theoretical_consumption" name="theoretical_consumption" invisible="1"/>
                <div class="o_row" name="theoretical_consumption_div" invisible="1">
                    <field name="theoretical_consumption" readonly="1"/>
                </div>
                <label for="real_consumption" name="real_consumption" invisible="1"/>
                <div class="o_row" name="real_consumption_div" invisible="1">
                    <field name="real_consumption" readonly="1"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="view_mrp_production_form_inherit" model="ir.ui.view">
        <field name="name">mrp.production.form.inherit</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">

            <xpath expr="//group[@name='group_extra_info']" position="after">
                <group string="Dimensions" name="dimensions">
                    <group>
                        <label for="length" string="Longueur"/>
                        <div class="o_row">
                            <field name="length" class="oe_inline"/>
                            <span class="ml4">mm</span>
                        </div>
                        <label for="width" string="Largeur"/>
                        <div class="o_row">
                            <field name="width" class="oe_inline"/>
                            <span class="ml4">mm</span>
                        </div>
                        <label for="area" string="Surface"/>
                        <div class="o_row">
                            <field name="area" class="oe_inline" readonly="1"/>
                            <span class="ml4">m²</span>
                        </div>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

</odoo>