<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_invoice_document_inherit" inherit_id="account.report_invoice_document">
        <xpath expr="//th[@name='th_priceunit']" position="replace">
            <th name="th_priceunit"
                t-attf-class="text-end {{ 'd-none d-md-table-cell' if report_type == 'html' else '' }}">
                <span>Prix Unité</span>
            </th>
        </xpath>
        <xpath expr="//th[@name='th_subtotal']" position="replace">
            <th name="th_subtotal" class="text-end">
                <span>Monatant HT</span>
            </th>
        </xpath>
        <xpath expr="//tbody[@class='invoice_tbody']" position="before">
            <t t-set="index" t-value="0"/>
        </xpath>
        <xpath expr="//th[@name='th_quantity']" position="replace">
        </xpath>
        <xpath expr="//td[@name='td_quantity']" position="replace">
        </xpath>
        <xpath expr="//td[@name='td_taxes']" position="replace">
        </xpath>
        <xpath expr="//th[@name='th_taxes']" position="replace">
        </xpath>
        <xpath expr="//th[@name='th_description']" position="before">
            <th name="th_num_article" class="text-start">
                <span>N° article</span>
            </th>
        </xpath>
        <xpath expr="//th[@name='th_description']" position="after">
            <th name="th_quantity" class="text-end">
                <span>Nbre</span>
            </th>
            <th name="th_length" class="text-end">
                <span>Longueur (mm)</span>
            </th>
            <th name="th_width" class="text-end">
                <span>Largeur (mm)</span>
            </th>
            <th name="th_surface" class="text-end">
                <span>Quantité</span>
            </th>
            <th name="th_unit" class="text-end">
                <span>Unité mesure</span>
            </th>
        </xpath>

        <xpath expr="//td[@name='account_invoice_line_name']" position="before">
            <t t-set="index" t-value="index + 1"/>
            <td>
                <span t-esc="index"/>
            </td>
        </xpath>

        <xpath expr="//td[@name='account_invoice_line_name']" position="after">
            <td name="td_quantity" class="text-end">
                <span t-field="line.quantity">3.00</span>
            </td>
            <td name="td_length" class="text-end">
                <span t-field="line.longueur" t-options="{'widget': 'float', 'precision': 2}"/>
            </td>
            <td name="td_width" class="text-end">
                <span t-field="line.largeur" t-options="{'widget': 'float', 'precision': 2}"/>
            </td>
            <td name="td_surface" class="text-end">
                <span t-field="line.surface" t-options="{'widget': 'float', 'precision': 2}"/>
            </td>
            <td name="td_unit" class="text-end">
                <span t-field="line.product_uom_id">units</span>
            </td>

        </xpath>
    </template>
</odoo> 