from odoo import fields, models


class MrpDimensionChange(models.Model):
    _name = 'mrp.dimension.change'
    _description = 'Historique des changements de dimensions'
    _order = 'create_date desc'

    bom_id = fields.Many2one('mrp.bom', string='Nomenclature', required=True, ondelete='cascade')
    old_length = fields.Float(string='Ancienne longueur (mm)')
    new_length = fields.Float(string='Nouvelle longueur (mm)')
    old_width = fields.Float(string='Ancienne largeur (mm)')
    new_width = fields.Float(string='Nouvelle largeur (mm)')
    old_surface = fields.Float(string='Ancienne surface (m²)')
    new_surface = fields.Float(string='Nouvelle surface (m²)')
    create_uid = fields.Many2one('res.users', string='Modifié par', readonly=True)
    create_date = fields.Datetime(string='Date de modification', readonly=True) 