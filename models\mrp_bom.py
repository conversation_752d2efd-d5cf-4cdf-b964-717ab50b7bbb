from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class MrpBomLine(models.Model):
    _inherit = 'mrp.bom.line'

    consumption_per_m2 = fields.Float(string='Consommation par m²', default=0.0)
    total_qty_with_loss = fields.Float(string='Quantité avec perte', compute='_compute_total_qty_with_loss')

    @api.depends('product_qty', 'bom_id.material_loss_percent')
    def _compute_total_qty_with_loss(self):
        for line in self:
            loss_factor = 1 + (line.bom_id.material_loss_percent or 0.0) / 100
            line.total_qty_with_loss = line.product_qty * loss_factor


class MrpBom(models.Model):
    _inherit = 'mrp.bom'

    length = fields.Float(string='Longueur (mm)', default=0.0)
    width = fields.Float(string='Largeur (mm)', default=0.0)
    surface_unit = fields.Float(string='Surface unitaire (m²)', compute='_compute_surface', store=True)
    material_loss_percent = fields.Float(string='Perte matière (%)', default=0.0)
    total_material_qty = fields.Float(string='Quantité totale avec perte', compute='_compute_total_material')
    dimension_change_ids = fields.One2many('mrp.dimension.change', 'bom_id', string='Historique des dimensions')

    @api.depends('length', 'width')
    def _compute_surface(self):
        for record in self:
            if record.length and record.width:
                record.surface_unit = (record.length / 1000) * (record.width / 1000)
            else:
                record.surface_unit = 0.0

    @api.depends('bom_line_ids.product_qty', 'material_loss_percent')
    def _compute_total_material(self):
        for bom in self:
            total_qty = sum(bom.bom_line_ids.mapped('product_qty'))
            # Ajouter le pourcentage de perte
            bom.total_material_qty = total_qty * (1 + bom.material_loss_percent / 100)

    @api.constrains('length', 'width')
    def _check_dimensions(self):
        for record in self:
            if record.length < 0 or record.width < 0:
                raise ValidationError(_("Les dimensions ne peuvent pas être négatives."))

    @api.onchange('length', 'width')
    def _onchange_dimensions(self):
        """Mettre à jour les quantités des composants en fonction de la surface"""
        if self.length and self.width:
            surface = (self.length / 1000) * (self.width / 1000)
            for line in self.bom_line_ids:
                if line.consumption_per_m2:
                    line.product_qty = surface * line.consumption_per_m2

    def write(self, vals):
        # Enregistrer l'historique des modifications de dimensions
        if 'length' in vals or 'width' in vals:
            for record in self:
                self.env['mrp.dimension.change'].create({
                    'bom_id': record.id,
                    'old_length': record.length,
                    'new_length': vals.get('length', record.length),
                    'old_width': record.width,
                    'new_width': vals.get('width', record.width),
                    'old_surface': record.surface_unit,
                    'new_surface': (vals.get('length', record.length) / 1000) * (vals.get('width', record.width) / 1000)
                })

        res = super(MrpBom, self).write(vals)
        if 'length' in vals or 'width' in vals:
            self._update_component_quantities()
        return res

    def _update_component_quantities(self):
        """Mettre à jour les quantités des composants"""
        for bom in self:
            if bom.length and bom.width:
                surface = (bom.length / 1000) * (bom.width / 1000)
                for line in bom.bom_line_ids:
                    if line.consumption_per_m2:
                        line.product_qty = surface * line.consumption_per_m2 * (1 + bom.material_loss_percent / 100)
