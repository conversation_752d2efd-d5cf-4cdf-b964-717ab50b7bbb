from odoo import models, fields, api, _
from odoo.tools import frozendict, format_date, float_compare, Query

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    longueur = fields.Float(string='Longueur (mm)', default=0.0)
    largeur = fields.Float(string='Largeur (mm)', default=0.0)
    surface = fields.Float(string='Surface (m²)', compute='_compute_surface', store=True)
    dimension_type = fields.Selection([
        ('none', 'Aucune dimension'),
        ('length_only', 'Longueur seulement'),
        ('length_width', 'Longueur et Largeur')
    ], string="Type de dimensions", default='length_width')
    pricing_type = fields.Selection([
        ('piece', 'Pièce'),
        ('metre', 'Mètre linéaire'),
        ('surface', 'Surface')
    ], string="Type de tarification", default='piece')
    surface_unit = fields.Float(string='Surface unitaire (m²)', store=True, compute='_compute_surface')
    total_length = fields.Float(string='Longueur totale (m)', store=True, compute='_compute_surface')

    @api.depends('display_type')
    def _compute_quantity(self):
        for line in self:
            if line.display_type == 'product':
                line.quantity = line.surface if line.surface else 1
            else:
                line.quantity = False

    def _prepare_edi_vals_to_export(self):
        ''' The purpose of this helper is the same as '_prepare_edi_vals_to_export' but for a single invoice line.
        This includes the computation of the tax details for each invoice line or the management of the discount.
        Indeed, in some EDI, we need to provide extra values depending the discount such as:
        - the discount as an amount instead of a percentage.
        - the price_unit but after subtraction of the discount.

        :return: A python dict containing default pre-processed values.
        '''
        self.ensure_one()

        if self.discount == 100.0:
            gross_price_subtotal = self.currency_id.round(self.price_unit * self.surface)
        else:
            gross_price_subtotal = self.currency_id.round(self.price_subtotal / (1 - self.discount / 100.0))

        res = {
            'line': self,
            'price_unit_after_discount': self.currency_id.round(self.price_unit * (1 - (self.discount / 100.0))),
            'price_subtotal_before_discount': gross_price_subtotal,
            'price_subtotal_unit': self.currency_id.round(self.price_subtotal / self.surface) if self.surface else 0.0,
            'price_total_unit': self.currency_id.round(self.price_total / self.surface) if self.surface else 0.0,
            'price_discount': gross_price_subtotal - self.price_subtotal,
            'price_discount_unit': (gross_price_subtotal - self.price_subtotal) / self.surface if self.surface else 0.0,
            'gross_price_total_unit': self.currency_id.round(
                gross_price_subtotal / self.surface) if self.surface else 0.0,
            'unece_uom_code': self.product_id.product_tmpl_id.uom_id._get_unece_code(),
        }
        return res


    def _convert_to_tax_base_line_dict(self):
        """ Convert the current record to a dictionary in order to use the generic taxes computation method
        defined on account.tax.
        :return: A python dictionary.
        """
        self.ensure_one()
        is_invoice = self.move_id.is_invoice(include_receipts=True)
        sign = -1 if self.move_id.is_inbound(include_receipts=True) else 1
        res = self.env['account.tax']._convert_to_tax_base_line_dict(
            self,
            partner=self.partner_id,
            currency=self.currency_id,
            product=self.product_id,
            taxes=self.tax_ids,
            price_unit=self.price_unit if is_invoice else self.amount_currency,
            quantity=self.surface if is_invoice else 1.0,
            discount=self.discount if is_invoice else 0.0,
            account=self.account_id,
            analytic_distribution=self.analytic_distribution,
            price_subtotal=self.price_unit * self.surface,
            is_refund=self.is_refund,
            rate=(abs(self.amount_currency) / abs(self.balance)) if self.balance else 1.0
        )
        return res

    @api.depends('quantity', 'discount', 'price_unit', 'tax_ids', 'currency_id', 'longueur', 'largeur', 'surface')
    def _compute_totals(self):
        for line in self:
            # Déterminer la quantité effective selon le type de dimensions
            if line.dimension_type == 'length_width' and line.surface > 0:
                # Pour les produits surfaciques, utiliser la surface
                quantity = line.surface
            elif line.dimension_type == 'length_only' and line.total_length > 0:
                # Pour les produits linéaires, utiliser la longueur totale
                quantity = line.total_length
            else:
                # Pour les produits sans dimensions, utiliser la quantité normale
                quantity = line.quantity

            # Calcul des taxes
            tax_results = self.env['account.tax'].with_company(line.company_id)._compute_taxes([
                line._convert_to_tax_base_line_dict()
            ])
            totals = list(tax_results['totals'].values())[0]
            amount_untaxed = quantity * line.price_unit * (1 - (line.discount or 0.0) / 100.0)
            amount_tax = totals['amount_tax']

            line.update({
                'price_subtotal': amount_untaxed,
                'price_total': amount_untaxed + amount_tax,
            })




    @api.depends('tax_ids', 'currency_id', 'partner_id', 'analytic_distribution', 'balance', 'partner_id',
                 'move_id.partner_id', 'price_unit', 'quantity', 'surface')
    def _compute_all_tax(self):
        for line in self:
            sign = line.move_id.direction_sign
            if line.display_type == 'tax':
                line.compute_all_tax = {}
                line.compute_all_tax_dirty = False
                continue
            if line.display_type == 'product' and line.move_id.is_invoice(True):
                amount_currency = sign * line.price_unit * (1 - line.discount / 100)
                handle_price_include = True
                quantity = line.surface
            else:
                amount_currency = line.amount_currency
                handle_price_include = False
                quantity = 1
            compute_all_currency = line.tax_ids.compute_all(
                amount_currency,
                currency=line.currency_id,
                quantity=quantity,
                product=line.product_id,
                partner=line.move_id.partner_id or line.partner_id,
                is_refund=line.is_refund,
                handle_price_include=handle_price_include,
                include_caba_tags=line.move_id.always_tax_exigible,
                fixed_multiplicator=sign,
            )
            rate = line.amount_currency / line.balance if line.balance else 1
            line.compute_all_tax_dirty = True
            line.compute_all_tax = {
                frozendict({
                    'tax_repartition_line_id': tax['tax_repartition_line_id'],
                    'group_tax_id': tax['group'] and tax['group'].id or False,
                    'account_id': tax['account_id'] or line.account_id.id,
                    'currency_id': line.currency_id.id,
                    'analytic_distribution': (tax['analytic'] or not tax[
                        'use_in_tax_closing']) and line.analytic_distribution,
                    'tax_ids': [(6, 0, tax['tax_ids'])],
                    'tax_tag_ids': [(6, 0, tax['tag_ids'])],
                    'partner_id': line.move_id.partner_id.id or line.partner_id.id,
                    'move_id': line.move_id.id,
                    'display_type': line.display_type,
                }): {
                    'name': tax['name'] + (' ' + _('(Discount)') if line.display_type == 'epd' else ''),
                    'balance': tax['amount'] / rate,
                    'amount_currency': tax['amount'],
                    'tax_base_amount': tax['base'] / rate * (-1 if line.tax_tag_invert else 1),
                }
                for tax in compute_all_currency['taxes']
                if tax['amount']
            }
            if not line.tax_repartition_line_id:
                line.compute_all_tax[frozendict({'id': line.id})] = {
                    'tax_tag_ids': [(6, 0, compute_all_currency['base_tags'])],
                }

    @api.depends('longueur', 'largeur', 'quantity', 'dimension_type')
    def _compute_surface(self):
        for record in self:
            # Calcul selon le type de dimensions
            if record.dimension_type == 'length_width' and record.longueur and record.largeur:
                # Produit avec longueur et largeur
                record.surface_unit = (record.longueur / 1000) * (record.largeur / 1000)
                record.total_length = (record.longueur / 1000) * record.quantity
                record.surface = record.surface_unit * record.quantity
            elif record.dimension_type == 'length_only' and record.longueur:
                # Produit avec longueur seulement
                record.total_length = (record.longueur / 1000) * record.quantity
                record.surface_unit = record.total_length
                record.surface = record.total_length
            else:
                # Produit sans dimensions
                record.surface_unit =1
                record.total_length = 1
                record.surface = 1


    @api.onchange('product_id')
    def _onchange_product_id_inherit_dimensions(self):
        """Hériter automatiquement les dimensions du produit"""
        if self.product_id:
            # Hériter les dimensions et types du produit
            self.dimension_type = getattr(self.product_id, 'dimension_type', 'none') or 'none'
            self.pricing_type = getattr(self.product_id, 'pricing_type', 'piece') or 'piece'

            # Hériter les dimensions selon le type
            if self.dimension_type == 'length_width':
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
                self.largeur = getattr(self.product_id, 'largeur', 1000.0) or 1000.0
            elif self.dimension_type == 'length_only':
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
                self.largeur = 1
            else:  # dimension_type == 'none'
                self.longueur = 0.0
                self.largeur = 0.0
