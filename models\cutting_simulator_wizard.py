from odoo import models, fields, api, _
import math

class CuttingSimulatorWizard(models.TransientModel):
    _name = 'cutting.simulator.wizard'
    _description = "Simulateur d'optimisation de découpe"

    sale_order_line_id = fields.Many2one('sale.order.line', string="Ligne de commande", required=True)
    largeur_bobine = fields.Float('Largeur bobine (mm)', required=True, default=1220)
    largeur_piece = fields.Float('Largeur pièce (mm)', required=True)
    longueur_max = fields.Integer('Longueur max pièce (mm)', required=True, default=3100)
    lineaire_client = fields.Integer('Linéaire client (mm)', required=True)
    nombre_plis = fields.Integer('Nombre de plis', required=True, default=1)
    montant_par_pli = fields.Float('Montant par pli (DA)', required=True, default=0.0)
    prix_base = fields.Float('Prix de base pièce', required=True, default=0.0)

    nb_pieces_par_tole = fields.Integer('Nombre de pièces par tôle', compute='_compute_nb_pieces_par_tole')
    longueur_optimale = fields.Integer('Longueur optimale (mm)', readonly=True)
    nb_pieces = fields.Integer('Nombre de pièces', readonly=True)
    lineaire_total = fields.Integer('Linéaire total (mm)', readonly=True)
    surplus = fields.Integer('Surplus (mm)', readonly=True)
    prix_unitaire_final = fields.Float('Prix unitaire final (DA)', readonly=True)
    product_id = fields.Many2one('product.product')

    @api.depends('largeur_bobine', 'largeur_piece')
    def _compute_nb_pieces_par_tole(self):
        for rec in self:
            rec.nb_pieces_par_tole = int(rec.largeur_bobine // rec.largeur_piece) if rec.largeur_piece else 0

    def action_calculer(self):
        longueur_max = self.longueur_max
        lineaire_client = self.lineaire_client
        nb_pieces_par_tole = self.nb_pieces_par_tole or 1

        best_surplus = None
        best_longueur = None
        best_nb_pieces = None

        for longueur in range(longueur_max, 0, -1):
            nb_pieces = math.ceil(lineaire_client / longueur)
            if nb_pieces % nb_pieces_par_tole != 0:
                nb_pieces = ((nb_pieces // nb_pieces_par_tole) + 1) * nb_pieces_par_tole
            lineaire_total = nb_pieces * longueur
            surplus = lineaire_total - lineaire_client
            if surplus >= 0:
                if best_surplus is None or surplus < best_surplus:
                    best_surplus = surplus
                    best_longueur = longueur
                    best_nb_pieces = nb_pieces
                if surplus == 0:
                    break

        self.longueur_optimale = best_longueur
        self.nb_pieces = best_nb_pieces
        self.lineaire_total = best_nb_pieces * best_longueur
        self.surplus = best_surplus
        self.prix_unitaire_final = self.prix_base + (self.nombre_plis * self.montant_par_pli)
        return {
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'view_type': 'form',
            'target': 'new',
        }

    def action_appliquer(self):
        self.sale_order_line_id.write({
            'longueur': self.longueur_optimale,
            'product_uom_qty': self.nb_pieces,
            'nombre_plis': self.nombre_plis,
            'price_unit': self.prix_unitaire_final,
        })
        return {'type': 'ir.actions.act_window_close'} 