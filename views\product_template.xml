<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="product_template_form_view" model="ir.ui.view">
            <field name="name">product.template.form.inherit</field>
            <field name="model">product.template</field>
            <field name="priority">5</field>
            <field name="inherit_id" ref="product.product_template_only_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='default_code']" position="after">
                    <field name="old_reference"/>
                </xpath>
                <xpath expr="//field[@name='detailed_type']" position="after">
                    <field name="dimension_type" widget="radio"/>
                    <field name="pricing_type"/>
                    <field name="longueur"
                           invisible="dimension_type == 'none'"
                           required="dimension_type in ['length_only', 'length_width']"/>
                    <field name="largeur"
                           invisible="dimension_type != 'length_width'"
                           required="dimension_type == 'length_width'"/>
                </xpath>

                <!-- Ajouter un onglet pour la configuration des dimensions -->
                <xpath expr="//notebook" position="inside">
                    <page string="Configuration Dimensions" name="dimensions_config">
                        <group>
                            <group string="Contraintes de dimensions"
                                   invisible="dimension_type == 'none'">
                                <field name="min_length"
                                       invisible="dimension_type == 'none'"/>
                                <field name="max_length"
                                       invisible="dimension_type == 'none'"/>
                                <field name="min_width"
                                       invisible="dimension_type != 'length_width'"/>
                                <field name="max_width"
                                       invisible="dimension_type != 'length_width'"/>
                            </group>
                            <group string="Informations dimensions">
                                <div invisible="dimension_type == 'none'" class="alert alert-info">
                                    <p><strong>Dimensions du produit :</strong></p>
                                    <p invisible="dimension_type != 'length_only'">
                                        • Ce produit nécessite uniquement une longueur<br/>
                                        • Tarification au mètre linéaire
                                    </p>
                                    <p invisible="dimension_type != 'length_width'">
                                        • Ce produit nécessite longueur ET largeur<br/>
                                        • Tarification à la surface (m²)
                                    </p>
                                </div>
                            </group>
                        </group>

                        <group string="Tarification spécifique">
                            <group>
                                <field name="price_per_meter"
                                       invisible="pricing_type != 'metre'"/>
                                <field name="price_per_m2"
                                       invisible="pricing_type != 'surface'"/>
                                <field name="montant_par_pli"/>
                            </group>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Vue liste des produits avec dimensions -->
        <record id="product_template_tree_view_inherit" model="ir.ui.view">
            <field name="name">product.template.tree.inherit.dimensions</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='list_price']" position="after">
                    <field name="dimension_type" optional="show"/>
                    <field name="longueur" optional="show"
                           invisible="dimension_type == 'none'"/>
                    <field name="largeur" optional="show"
                           invisible="dimension_type != 'length_width'"/>
                    <field name="pricing_type" optional="hide"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
