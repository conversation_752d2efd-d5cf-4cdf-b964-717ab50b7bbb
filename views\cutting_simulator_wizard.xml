<odoo>
    <record id="view_cutting_simulator_wizard_form" model="ir.ui.view">
        <field name="name">cutting.simulator.wizard.form</field>
        <field name="model">cutting.simulator.wizard</field>
        <field name="arch" type="xml">
            <form string="Simulateur d'optimisation de découpe">
                <group>
                    <field name="product_id"/>
                    <field name="largeur_bobine"/>
                    <field name="largeur_piece"/>
                    <field name="longueur_max" invisible=""/>
                    <field name="lineaire_client"/>
                    <field name="nombre_plis"/>
                    <field name="montant_par_pli"/>
                    <field name="prix_base"/>
                    <field name="nb_pieces_par_tole" readonly="1"/>
                </group>

                <!-- Résultats après calcul -->
                <group string="Résultats de l'optimisation">
                    <field name="longueur_optimale" readonly="1"/>
                    <field name="nb_pieces" readonly="1"/>
                    <field name="lineaire_total" readonly="1"/>
                    <field name="surplus" readonly="1"/>
                    <field name="prix_unitaire_final" readonly="1"/>
                </group>

                <!-- Un seul footer avec tous les boutons -->
                <footer>
                    <button name="action_calculer" string="Calculer l'optimisation" type="object" class="btn-primary"/>
                    <button name="action_appliquer" string="Appliquer au devis" type="object" class="btn-success"/>
                    <button string="Fermer" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>


    <record id="action_cutting_simulator_wizard" model="ir.actions.act_window">
        <field name="name">Simulateur de découpe</field>
        <field name="res_model">cutting.simulator.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_cutting_simulator_wizard_form"/>
        <field name="target">new</field>
    </record>

</odoo> 