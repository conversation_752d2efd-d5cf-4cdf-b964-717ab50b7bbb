from odoo import fields, models, api
import logging

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_confirm(self):
        """Override to create manufacturing orders for MTO products with dimensions"""
        res = super(SaleOrder, self).action_confirm()

        try:
            # After confirming the sale order, check for MTO products and create manufacturing orders
            for order in self:
                for line in order.order_line:
                    # Vérifier si le produit a une nomenclature
                    # Utiliser la méthode _bom_find avec la signature correcte pour Odoo 17
                    bom = self.env['mrp.bom']._bom_find(line.product_id)

                    # Vérifier si le produit est configuré en MTO
                    # Vérifier directement les routes du produit sans rechercher la route MTO spécifique
                    # Un produit est considéré comme MTO s'il a une route avec un nom contenant 'Make To Order' ou similaire
                    is_mto = False
                    mto_names = ['Make To Order', 'Sur Commande', 'Fabriquer sur commande', 'MTO']
                    for route in line.product_id.route_ids:
                        # Vérifier si le nom de la route contient l'un des noms MTO connus
                        if any(mto_name.lower() in route.name.lower() for mto_name in mto_names):
                            is_mto = True
                            break

                    if bom and is_mto:
                        # Check if a manufacturing order already exists for this sale order line
                        existing_mo = self.env['mrp.production'].search([
                            ('origin', '=', order.name),
                            ('product_id', '=', line.product_id.id),
                            ('state', '!=', 'cancel')
                        ])

                        # If no manufacturing order exists, create one
                        if not existing_mo:
                            # Ne pas modifier product_qty, seulement transférer les dimensions
                            # product_uom_qty sera calculé automatiquement par la méthode update_component_line
                            # en fonction de la surface et de la quantité de la nomenclature
                            vals = {
                                'product_id': line.product_id.id,
                                'product_qty': line.product_uom_qty,  # Utiliser la quantité de la ligne de commande
                                'product_uom_id': line.product_uom.id,
                                'bom_id': bom.id,
                                'origin': order.name,
                                'length': line.longueur or 0.0,
                                'width': line.largeur or 0.0,
                                'area': line.surface or 0.0,
                            }
                            new_mo = self.env['mrp.production'].create(vals)
                            # Link the manufacturing order to the sale order line
                            line.write({'move_dest_ids': [(4, move.id) for move in new_mo.move_finished_ids]})
        except Exception as e:
            _logger.error(f"Erreur lors de la création de l'ordre de fabrication: {e}")
            # Ne pas bloquer la confirmation du devis en cas d'erreur

        return res


    # @api.depends('order_line.price_subtotal', 'order_line.price_tax', 'order_line.price_total')
    # def _compute_amounts(self):
    #     """Compute the total amounts of the SO."""
    #     for order in self:
    #         order = order.with_company(order.company_id)
    #         order_lines = order.order_line.filtered(lambda x: not x.display_type)
    #
    #         if order.company_id.tax_calculation_rounding_method == 'round_globally':
    #             tax_results = order.env['account.tax']._compute_taxes([
    #                 line._convert_to_tax_base_line_dict()
    #                 for line in order_lines
    #             ])
    #             totals = tax_results['totals']
    #             amount_untaxed = totals.get(order.currency_id, {}).get('amount_untaxed', 0.0)
    #             amount_tax = totals.get(order.currency_id, {}).get('amount_tax', 0.0)
    #         else:
    #             amount_untaxed = sum(order_lines.mapped('price_subtotal'))
    #             print("***************amount_untaxed***************")
    #             print(amount_untaxed)
    #             amount_tax = sum(order_lines.mapped('price_tax'))
    #
    #         order.amount_untaxed = amount_untaxed
    #         print("*******order.amount_untaxed*******")
    #         print(order.amount_untaxed)
    #         order.amount_tax = amount_tax
    #         order.amount_total = order.amount_untaxed + order.amount_tax
