from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
from odoo.fields import Command


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Dimensions selon le type de produit
    longueur = fields.Float(string='Longueur (mm)', store=True, default=1.00)
    largeur = fields.Float(string='Largeur (mm)', store=True, default=1.00)
    surface = fields.Float(string='Surface (m²)', store=True, default=1.00, compute='_compute_surface')
    surface_unit = fields.Float(string='Surface unitaire (m²)', store=True, compute='_compute_surface')
    total_length = fields.Float(string='Longueur totale (m)', store=True, compute='_compute_surface')
    nombre_plis = fields.Integer(string="Nombre de plis", default=1)

    # Champs de dimensions (stockés directement)
    dimension_type = fields.Selection([
        ('none', 'Aucune dimension'),
        ('length_only', 'Longueur seulement'),
        ('length_width', 'Longueur et Largeur')
    ], string="Type de dimensions", default='length_width')

    pricing_type = fields.Selection([
        ('piece', 'Pièce'),
        ('metre', 'Mètre linéaire'),
        ('surface', 'Surface')
    ], string="Type de tarification", default='piece')

    def open_wizard_simulator(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'sale_product_dimensionV1.action_cutting_simulator_wizard')
        context = {
            'default_sale_order_line_id': self.id,
            'default_product_id': self.product_id.id
        }
        action['context'] = context
        return action

    @api.depends('longueur', 'largeur', 'product_uom_qty', 'dimension_type')
    def _compute_surface(self):
        for record in self:
            # Calcul selon le type de dimensions
            if record.dimension_type == 'length_width' and record.longueur and record.largeur:
                # Produit avec longueur et largeur
                record.surface_unit = (record.longueur / 1000) * (record.largeur / 1000)
                record.total_length = (record.longueur / 1000) * record.product_uom_qty
                record.surface = record.surface_unit * record.product_uom_qty
            elif record.dimension_type == 'length_only' and record.longueur:
                # Produit avec longueur seulement
                record.total_length = (record.longueur / 1000) * record.product_uom_qty
                record.surface_unit = record.total_length
                record.surface = record.total_length
                print(f'TEST total_length {record.total_length}')
                print(f'TEST surface_unit {record.surface_unit}')
                print(f'TEST surface {record.surface}')
            else:
                # Produit sans dimensions
                record.surface_unit = 1.0
                record.total_length = 1.0
                record.surface = 1.0

    @api.constrains('longueur', 'largeur')
    def _check_dimensions(self):
        for record in self:
            if record.longueur < 0 or record.largeur < 0:
                raise ValidationError(_("Les dimensions ne peuvent pas être négatives."))

    @api.onchange('product_id')
    def _onchange_product_id_inherit_dimensions(self):
        """Hériter automatiquement les dimensions du produit"""
        if self.product_id:
            # Hériter les dimensions et types du produit
            self.dimension_type = getattr(self.product_id, 'dimension_type', 'none') or 'none'
            self.pricing_type = getattr(self.product_id, 'pricing_type', 'piece') or 'piece'

            # Hériter les dimensions selon le type
            if self.dimension_type == 'length_width':
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
                self.largeur = getattr(self.product_id, 'largeur', 1000.0) or 1000.0
            elif self.dimension_type == 'length_only':
                self.largeur = 1
                self.longueur = getattr(self.product_id, 'longueur', 1000.0) or 1000.0
            else:  # dimension_type == 'none'
                self.longueur = 0.0
                self.largeur = 0.0

    # @api.onchange('dimension_type')
    # def _onchange_dimension_type(self):
    #     """Réagir aux changements de type de dimensions"""
    #     if self.dimension_type == 'none':
    #         self.longueur = 0.0
    #         self.largeur = 0.0
    #     elif self.dimension_type == 'length_only':
    #         if self.longueur == 0.0:
    #             self.longueur = 1000.0
    #         self.largeur = 0.0
    #     elif self.dimension_type == 'length_width':
    #         if self.longueur == 0.0:
    #             self.longueur = 1000.0
    #         if self.largeur == 0.0:
    #             self.largeur = 1000.0

    @api.onchange('longueur', 'largeur', 'product_uom_qty')
    def _onchange_dimensions(self):
        if self.product_id:
            # Calcul du prix selon le type de tarification
            if self.pricing_type == 'surface' and self.surface_unit > 0:
                self.price_unit = getattr(self.product_id, 'price_per_m2', 0.0) or self.product_id.list_price
            elif self.pricing_type == 'metre' and self.longueur > 0:
                self.price_unit = getattr(self.product_id, 'price_per_meter', 0.0) or self.product_id.list_price
            # Pour 'piece', garder le prix standard du produit

    def _convert_to_tax_base_line_dict(self):
        """ Calcul du montant de base pour les taxes en tenant compte de la surface """
        result = super()._convert_to_tax_base_line_dict()

        if self.longueur and self.largeur:
            # La quantité effective est la surface totale
            result['quantity'] = self.surface

            # Ajuster le prix unitaire si nécessaire
            if hasattr(self.product_id, 'surface_price'):
                result['price_unit'] = self.product_id.surface_price

        return result

    @api.depends('product_id','product_uom_qty', 'discount', 'price_unit', 'tax_id', 'longueur', 'largeur', 'surface',
                 'dimension_type')
    def _compute_amount(self):
        """Calcul des montants en tenant compte des dimensions"""
        print(f'\n\n HELLO RATAUA')
        for line in self:
            # Déterminer la quantité effective selon le type de dimensions
            if line.dimension_type == 'length_width' and line.surface > 0:
                # Pour les produits surfaciques, utiliser la surface
                quantity = line.surface
            elif line.dimension_type == 'length_only' and line.total_length > 0:
                # Pour les produits linéaires, utiliser la longueur totale
                quantity = line.total_length
            else:
                # Pour les produits sans dimensions, utiliser la quantité normale
                quantity = line.product_uom_qty

            # Calcul des taxes
            tax_results = self.env['account.tax'].with_company(line.company_id)._compute_taxes([
                line._convert_to_tax_base_line_dict()
            ])
            totals = list(tax_results['totals'].values())[0]
            amount_untaxed = quantity * line.price_unit * (1 - (line.discount or 0.0) / 100.0)
            amount_tax = totals['amount_tax']

            line.sudo().write({
                'price_subtotal': amount_untaxed,
                'price_tax': amount_tax,
                'price_total': amount_untaxed + amount_tax,
            })


    def _prepare_invoice_line(self, **optional_values):
            """Prepare the values to create the new invoice line for a sales order line.

            :param optional_values: any parameter that should be added to the returned invoice line
            :rtype: dict
            """
            self.ensure_one()
            res = {
                'display_type': self.display_type or 'product',
                'sequence': self.sequence,
                'name': self.name,
                'product_id': self.product_id.id,
                'product_uom_id': self.product_uom.id,
                'quantity': self.qty_to_invoice,
                'discount': self.discount,
                'price_unit': self.price_unit,
                'tax_ids': [Command.set(self.tax_id.ids)],
                'sale_line_ids': [Command.link(self.id)],
                'longueur': self.longueur,
                'largeur': self.largeur,
                'surface': self.surface,
                'dimension_type': self.dimension_type,
                'pricing_type': self.pricing_type,
            }
            self._set_analytic_distribution(res, **optional_values)
            if optional_values:
                res.update(optional_values)
            if self.display_type:
                res['account_id'] = False
            return res
