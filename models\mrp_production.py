from odoo import fields, models, api
import logging
from datetime import datetime, timedelta


class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    length = fields.Float(string='Longueur (mm)')
    width = fields.Float(string='Largeur (mm)')
    area = fields.Float(string='Surface (m²)', compute='_compute_area', store=True)
    theoretical_consumption = fields.Float(string='Consommation Théorique', compute='_compute_consumption', store=True)
    real_consumption = fields.Float(string='Consommation Réelle', compute='_compute_consumption', store=True)
    sale_line_id = fields.Many2one('sale.order.line', string='Ligne de commande', compute='_compute_sale_line', store=True)
    sale_order_count = fields.Integer(string="Nombre de commandes de vente")

    @api.depends('origin')
    def _compute_sale_line(self):
        for production in self:
            if production.origin:
                # Rechercher la commande de vente
                sale_order = self.env['sale.order'].search([('name', '=', production.origin)], limit=1)
                if sale_order:
                    # Trouver la ligne de commande correspondante
                    sale_line = sale_order.order_line.filtered(lambda l: l.product_id == production.product_id)
                    if sale_line:
                        production.sale_line_id = sale_line[0]
                    else:
                        production.sale_line_id = False
            else:
                production.sale_line_id = False

    @api.depends('sale_line_id', 'sale_line_id.longueur', 'sale_line_id.largeur', 'sale_line_id.surface', 'sale_line_id.product_uom_qty')
    def _compute_dimensions(self):
        for production in self:
            if production.sale_line_id:
                production.length = production.sale_line_id.longueur
                production.width = production.sale_line_id.largeur
                # production.product_qty = production.sale_line_id.product_uom_qty
                production.area = production.sale_line_id.surface

    @api.depends('length', 'width', 'product_qty')
    def _compute_area(self):
        for record in self:
            if record.length and record.width and record.product_qty:
                # Surface totale = Quantité commandée * (Longueur * Largeur) en m²
                record.area = record.product_qty * (record.length / 1000) * (record.width / 1000)
            else:
                record.area = 0.0

    @api.depends('area', 'bom_id', 'move_raw_ids.product_uom_qty')
    def _compute_consumption(self):
        for record in self:
            if record.area and record.bom_id:
                # Calcul basé sur la surface et la BOM
                record.theoretical_consumption = record.area * record.bom_id.product_qty
                # La consommation réelle est la somme des quantités des composants
                record.real_consumption = sum(record.move_raw_ids.mapped('product_uom_qty'))
            else:
                record.theoretical_consumption = 0.0
                record.real_consumption = 0.0

    @api.onchange('length', 'width', 'area')
    def onchange_dimensions(self):
        if self.area:
            self.update_component_line()

    def update_component_line(self):
        for line in self.move_raw_ids:
            bom_id = self.bom_id
            if not bom_id:
                continue

            # Calculer product_uom_qty en fonction de la surface et de la quantité de la nomenclature
            if self.area and bom_id:
                bom_line = self.env['mrp.bom.line'].search([
                    ('bom_id', '=', bom_id.id),
                    ('product_id', '=', line.product_id.id)
                ], limit=1)

                if bom_line:
                    # Calculer la quantité à consommer : Surface * Quantité BOM
                    line.product_uom_qty = self.area * bom_line.product_qty

    @api.model
    def create(self, vals):
        result = super(MrpProduction, self).create(vals)
        # Mettre à jour les dimensions depuis la commande de vente
        result._compute_dimensions()
        # Mettre à jour les composants
        if result.area:
            result.update_component_line()
        return result

    def write(self, vals):
        res = super(MrpProduction, self).write(vals)
        # Recalculer les dimensions si nécessaire
        if any(field in vals for field in ['origin', 'product_id']):
            self._compute_dimensions()
        # Mettre à jour les composants si les dimensions ont changé
        if any(field in vals for field in ['length', 'width', 'area', 'product_qty']):
            self.update_component_line()
        return res

    def action_confirm(self):
        res = super(MrpProduction, self).action_confirm()
        for production in self:
            # S'assurer que les dimensions sont à jour
            production._compute_dimensions()
            if production.area:
                production.update_component_line()
        return res

    def _get_move_raw_values(self, bom_line, location_id, location_dest_id, company_id, product_qty=False):
        vals = super(MrpProduction, self)._get_move_raw_values(
            bom_line,
            location_id,
            location_dest_id,
            company_id,
            product_qty
        )
        
        if self.area and self.bom_id:
            # Si bom_line est un product.product, on doit trouver la ligne de nomenclature correspondante
            if isinstance(bom_line, self.env['product.product'].__class__):
                bom_line_id = self.bom_id.bom_line_ids.filtered(lambda l: l.product_id == bom_line)
                if bom_line_id:
                    vals['product_uom_qty'] = self.area * bom_line_id.product_qty
            # Si c'est déjà une ligne de nomenclature
            elif hasattr(bom_line, 'product_qty'):
                vals['product_uom_qty'] = self.area * bom_line.product_qty
        
        return vals

    def _get_last_day_of_month(self, date):
        """Get the last day of the month for a given date."""
        if date.month == 12:
            next_month = date.replace(year=date.year + 1, month=1, day=1)
        else:
            next_month = date.replace(month=date.month + 1, day=1)
        last_day = next_month - timedelta(days=1)
        return last_day
