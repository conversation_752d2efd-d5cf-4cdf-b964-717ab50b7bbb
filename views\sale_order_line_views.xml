<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    Extended sale_order_form_view-->
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">
            sale.order.view.form.inherit.sale.product.dimension
        </field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Remove the dimension fields from after product_template_id -->

            <!-- Add the dimension fields after the product description (name field) -->
            <!--            <xpath expr="//field[@name='order_line']/tree/field[@name='name']"-->
            <!--                   position="after">-->
            <!--                <field name="longueur"/>-->
            <!--                <field name="largeur"/>-->
            <!--                <field name="surface"/>-->
            <!--            </xpath>-->
            <page name="order_lines" position="replace">
                <page string="<PERSON>gnes de commande" name="order_lines">
                    <field name="order_line" widget="section_and_note_one2many" mode="tree,kanban"
                           readonly="state == 'cancel' or locked" on_change="1" field_id="order_line_0">
                        <form>
                            <field name="display_type" invisible="1" on_change="1"/>
                            <!--
                                We need the sequence field to be here for new lines to be added at the correct position.
                                TODO: at some point we want to fix this in the framework so that an invisible field is not required.
                            -->
                            <field name="sequence" invisible="1"/>
                            <field name="product_uom_category_id" invisible="1"/>
                            <group>
                                <group invisible="display_type">
                                    <field name="product_updatable" invisible="1"/>
                                    <field name="product_id" domain="[('sale_ok', '=', True)]"
                                           context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'company_id': parent.company_id}"
                                           readonly="not product_updatable" required="not display_type" force_save="1"
                                           widget="many2one_barcode" on_change="1" can_create="True" can_write="True"/>
                                    <field name="product_type" invisible="1" on_change="1"/>
                                    <field name="invoice_status" invisible="1" on_change="1"/>
                                    <field name="qty_to_invoice" invisible="1" on_change="1"/>
                                    <field name="qty_delivered_method" invisible="1" on_change="1"/>
                                    <field name="price_total" invisible="1" on_change="1"/>
                                    <field name="price_tax" invisible="1" on_change="1"/>
                                    <field name="price_subtotal" invisible="1" on_change="1"/>
                                    <field name="product_uom_readonly" invisible="1"/>
                                    <label for="product_uom_qty"/>
                                    <div class="o_row" name="ordered_qty">
                                        <field context="{'partner_id':parent.partner_id, 'quantity':product_uom_qty, 'pricelist':parent.pricelist_id, 'uom':product_uom, 'uom_qty_change':True, 'company_id': parent.company_id}"
                                               name="product_uom_qty" on_change="1"/>
                                        <!-- below fields are used in the widget qty_at_date_widget -->
                                        <field name="virtual_available_at_date" invisible="1"/>
                                        <field name="qty_available_today" invisible="1"/>
                                        <field name="free_qty_today" invisible="1"/>
                                        <field name="scheduled_date" invisible="1"/>
                                        <field name="forecast_expected_date" invisible="1"/>
                                        <field name="warehouse_id" invisible="1" on_change="1"/>
                                        <field name="move_ids" invisible="1" on_change="1"/>
                                        <field name="qty_to_deliver" invisible="1"/>
                                        <field name="is_mto" invisible="1"/>
                                        <field name="display_qty_widget" invisible="1"/>
                                        <widget name="qty_at_date_widget"/>
                                        <field name="product_uom" force_save="1" class="oe_no_button"
                                               readonly="product_uom_readonly" required="not display_type" on_change="1"
                                               can_create="True" can_write="True"/>
                                    </div>
                                    <label for="qty_delivered" string="Délivré" invisible="parent.state != 'sale'"/>
                                    <div name="delivered_qty" invisible="parent.state != 'sale'">
                                        <field name="qty_delivered" readonly="qty_delivered_method != 'manual'"
                                               on_change="1"/>
                                    </div>
                                    <label for="qty_invoiced" string="Facturé" invisible="parent.state != 'sale'"/>
                                    <div name="invoiced_qty" invisible="parent.state != 'sale'">
                                        <field name="qty_invoiced" on_change="1"/>
                                    </div>
                                    <field name="price_unit" on_change="1"/>
                                    <field name="tax_id" widget="many2many_tags" options="{'no_create': True}"
                                           context="{'search_view_ref': 'account.account_tax_view_search'}"
                                           domain="[('type_tax_use', '=', 'sale'), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                           readonly="qty_invoiced &gt; 0" on_change="1" can_create="True"
                                           can_write="True"/>

                                    <!-- Champs de dimensions pour la vue formulaire -->
                                    <field name="dimension_type" invisible="1"/>
                                    <field name="longueur"
                                           string="Longueur (mm)"
                                           invisible="dimension_type == 'none'"
                                           required="dimension_type in ['length_only', 'length_width']"/>
                                    <field name="largeur"
                                           string="Largeur (mm)"
                                           invisible="dimension_type != 'length_width'"
                                           required="dimension_type == 'length_width'"/>
                                    <field name="surface"
                                           string="Surface (m²)"
                                           invisible="dimension_type != 'length_width'"
                                           readonly="1"/>
                                    <field name="total_length"
                                           string="Longueur totale (m)"
                                           invisible="dimension_type == 'none'"
                                           readonly="1"/>

                                    <!--
                                        We need the sequence field to be here
                                        because we want to be able to overwrite the default sequence value in the JS
                                        in order for new lines to be added at the correct position.
                                        NOTE: at some point we want to fix this in the framework so that an invisible field is not required.
                                    -->
                                    <field name="sequence" invisible="1"/>
                                </group>
                                <group invisible="display_type">
                                    <label for="customer_lead"/>
                                    <div name="lead">
                                        <field name="customer_lead" class="oe_inline" on_change="1"/>
                                        jours
                                    </div>
                                    <field name="route_id" options="{'no_create': True}" on_change="1" can_create="True"
                                           can_write="True"/>
                                </group>
                            </group>
                            <label for="name" string="Description" invisible="display_type"/>
                            <label for="name" string="Nom de section (par ex. Produits, Services)"
                                   invisible="display_type != 'line_section'"/>
                            <label for="name" string="Note" invisible="display_type != 'line_note'"/>
                            <field name="name"/>
                            <div name="invoice_lines" invisible="display_type">
                                <label for="invoice_lines"/>
                                <field name="invoice_lines" on_change="1" can_create="True" can_write="True"/>
                            </div>
                            <field name="state" invisible="1" on_change="1"/>
                            <field name="company_id" invisible="1" on_change="1"/>
                        </form>
                        <tree string="Lignes de commande" editable="bottom" limit="200">
                            <control>
                                <create name="add_product_control" string="Ajouter un produit"/>
                                <create name="add_section_control" string="Ajouter une section"
                                        context="{'default_display_type': 'line_section'}"/>
                                <create name="add_note_control" string="Ajouter une note"
                                        context="{'default_display_type': 'line_note'}"/>
                                <button name="action_add_from_catalog" string="Catalogue" type="object"
                                        class="px-4 btn-link" context="{'order_id': parent.id}"/>
                            </control>
                            <button invisible="pricing_type != 'piece'" icon="fa-outdent" class="oe_stat_button" string="Simulateur"
                                    name="open_wizard_simulator" type="object"
                            >Ouvrir le simulateur</button>
                            <field name="sequence" widget="handle"/>
                            <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                            <field name="pricing_type" column_invisible="True" on_change="1"/>
                            <field name="display_type" column_invisible="True" on_change="1"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="product_type" column_invisible="True" on_change="1"/>
                            <field name="product_updatable" column_invisible="True"/>
                            <field name="is_downpayment" column_invisible="True"/>
                            <field name="product_id" readonly="not product_updatable" required="not display_type"
                                   force_save="1"
                                   context="{                                         'partner_id': parent.partner_id,                                         'quantity': product_uom_qty,                                         'pricelist': parent.pricelist_id,                                         'uom':product_uom,                                         'company_id': parent.company_id,                                         'default_lst_price': price_unit,                                         'default_description_sale': name                                     }"
                                   options="{                                         'no_open': True,                                     }"
                                   domain="[('sale_ok', '=', True)]" widget="sol_product_many2one" optional="hide"
                                   string="Variante de produit" on_change="1" can_create="True" can_write="True"/>
                            <field name="product_template_id" string="Produit" column_invisible="0"
                                   readonly="not product_updatable" required="not display_type"
                                   context="{                                         'partner_id': parent.partner_id,                                         'quantity': product_uom_qty,                                         'pricelist': parent.pricelist_id,                                         'uom':product_uom,                                         'company_id': parent.company_id,                                         'default_list_price': price_unit,                                         'default_description_sale': name                                     }"
                                   options="{                                         'no_open': True,                                     }"
                                   domain="[('sale_ok', '=', True)]" widget="sol_product_many2one"
                                   placeholder="Tapez pour trouver un produit..." can_create="True" can_write="True"/>
                            <field name="product_template_attribute_value_ids" column_invisible="1"/>
                            <field name="product_custom_attribute_value_ids" column_invisible="1">
                                <tree>
                                    <field name="custom_product_template_attribute_value_id"/>
                                    <field name="custom_value"/>
                                </tree>
                            </field>
                            <field name="product_no_variant_attribute_value_ids" column_invisible="1" can_create="True"
                                   can_write="True"/>
                            <field name="is_configurable_product" column_invisible="1"/>
                            <field name="name" widget="section_and_note_text" optional="show"/>
                            <field name="product_uom_qty"
                                   decoration-info="(not display_type and invoice_status == 'to invoice')"
                                   decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                   context="{                                         'partner_id': parent.partner_id,                                         'quantity': product_uom_qty,                                         'pricelist': parent.pricelist_id,                                         'uom': product_uom,                                         'company_id': parent.company_id                                     }"
                                   readonly="is_downpayment"
                                   string="Nombre"
                                   on_change="1"/>
                            <!-- Champs de dimensions avec masquage intelligent -->
                            <field name="dimension_type" invisible="1"/>
                            <field name="longueur"
                                   string="Longueur (mm)"
                                   invisible="dimension_type == 'none'"
                                   optional="show"/>
                            <field name="largeur"
                                   string="Largeur (mm)"
                                   invisible="dimension_type != 'length_width'"
                                   optional="show"/>
                            <field name="surface"
                                   string="Surface (m²)"
                                   invisible="dimension_type != 'length_width'"
                                   readonly="1"
                                   optional="show"/>
                            <field name="total_length"
                                   string="Long. totale (m)"
                                   invisible="dimension_type == 'none'"
                                   readonly="1"
                                   optional="hide"/>
                            <field name="route_id" options="{'no_create': True}" optional="hide" on_change="1"
                                   can_create="True" can_write="True"/>
                            <field name="qty_delivered"
                                   decoration-info="(not display_type and invoice_status == 'to invoice')"
                                   decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                   string="Délivré" column_invisible="parent.state != 'sale'"
                                   readonly="qty_delivered_method != 'manual' or is_downpayment" optional="show"
                                   on_change="1"/>
                            <!-- below fields are used in the widget qty_at_date_widget -->
                            <field name="virtual_available_at_date" column_invisible="True"/>
                            <field name="qty_available_today" column_invisible="True"/>
                            <field name="free_qty_today" column_invisible="True"/>
                            <field name="scheduled_date" column_invisible="True"/>
                            <field name="forecast_expected_date" column_invisible="True"/>
                            <field name="warehouse_id" column_invisible="True" on_change="1"/>
                            <field name="move_ids" column_invisible="True" on_change="1"/>
                            <field name="qty_to_deliver" column_invisible="True"/>
                            <field name="is_mto" column_invisible="True"/>
                            <field name="display_qty_widget" column_invisible="True"/>
                            <widget name="qty_at_date_widget" width="20px"/>
                            <field name="qty_delivered_method" column_invisible="True" on_change="1"/>
                            <field name="qty_invoiced"
                                   decoration-info="(not display_type and invoice_status == 'to invoice')"
                                   decoration-bf="(not display_type and invoice_status == 'to invoice')"
                                   string="Facturé" column_invisible="parent.state != 'sale'" optional="show"
                                   on_change="1"/>
                            <field name="qty_to_invoice" column_invisible="True" on_change="1"/>
                            <field name="product_uom_readonly" column_invisible="True"/>
                            <field name="product_uom" force_save="1" string="UdM" readonly="product_uom_readonly"
                                   required="not display_type" context="{'company_id': parent.company_id}"
                                   options="{&quot;no_open&quot;: True}" optional="show" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="customer_lead" optional="hide"
                                   readonly="parent.state not in ['draft', 'sent', 'sale'] or is_downpayment"
                                   on_change="1"/>
                            <field name="price_unit" readonly="qty_invoiced &gt; 0" on_change="1"/>
                            <field name="tax_id" widget="many2many_tags" options="{'no_create': True}"
                                   domain="[('type_tax_use', '=', 'sale'), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                   context="{'active_test': True}" readonly="qty_invoiced &gt; 0 or is_downpayment"
                                   optional="show" on_change="1" can_create="True" can_write="True"/>
                            <field name="is_downpayment" column_invisible="True"/>
                            <field name="price_subtotal" string="Hors taxes" invisible="is_downpayment" on_change="1"/>
                            <field name="price_total" string="Toutes taxes comprises"
                                   column_invisible="parent.tax_calculation_rounding_method == 'round_globally'"
                                   invisible="is_downpayment" optional="hide" on_change="1"/>
                            <!-- Others fields -->
                            <field name="tax_calculation_rounding_method" column_invisible="True"/>
                            <field name="state" column_invisible="True" on_change="1"/>
                            <field name="invoice_status" column_invisible="True" on_change="1"/>
                            <field name="currency_id" column_invisible="True"/>
                            <field name="price_tax" column_invisible="True" on_change="1"/>
                            <field name="company_id" column_invisible="True" on_change="1"/>
                        </tree>
                        <kanban class="o_kanban_mobile">
                            <field name="name"/>
                            <field name="product_id" on_change="1"/>
                            <field name="product_uom_qty" on_change="1"/>
                            <field name="product_uom" on_change="1"/>
                            <field name="price_subtotal" on_change="1"/>
                            <field name="price_total" on_change="1"/>
                            <field name="price_tax" on_change="1"/>
                            <field name="price_total" on_change="1"/>
                            <field name="price_unit" on_change="1"/>
                            <field name="display_type" on_change="1"/>
                            <field name="tax_id" on_change="1"/>
                            <field name="company_id" on_change="1"/>
                            <field name="tax_calculation_rounding_method"/>
                            <control>
                                <create name="add_product_control" string="Ajouter un produit"/>
                                <create name="add_section_control" string="Ajouter une section"
                                        context="{'default_display_type': 'line_section'}"/>
                                <create name="add_note_control" string="Ajouter une note"
                                        context="{'default_display_type': 'line_note'}"/>
                                <button name="action_add_from_catalog" context="{'order_id': parent.id}"
                                        string="Catalogue" type="object" class="btn-secondary"/>
                            </control>
                            <templates>
                                <t t-name="kanban-box">
                                    <div t-attf-class="oe_kanban_card oe_kanban_global_click ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                        <t t-if="!record.display_type.raw_value">
                                            <div class="row g-0">
                                                <div class="col-2 pe-3">
                                                    <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)"
                                                         t-att-title="record.product_id.value"
                                                         t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                </div>
                                                <div class="col-10">
                                                    <div class="row">
                                                        <div class="col">
                                                            <strong t-out="record.product_id.value"/>
                                                        </div>
                                                        <div class="col-auto">
                                                            <strong>Hors taxes :</strong>
                                                            <t t-set="line_price"
                                                               t-value="record.price_subtotal.value"/>
                                                            <strong class="float-end text-end" t-out="line_price"/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-12 text-muted">
                                                            Quantité :
                                                            <t t-out="record.product_uom_qty.value"/>
                                                            <t t-out="record.product_uom.value"/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col text-muted">
                                                            Prix unitaire :
                                                            <t t-out="record.price_unit.value"/>
                                                        </div>
                                                        <div class="col-auto"
                                                             t-if="record.tax_calculation_rounding_method.raw_value === 'round_per_line'">
                                                            <strong>Toutes taxes comprises :</strong>
                                                            <t t-set="line_price" t-value="record.price_total.value"/>
                                                            <strong class="float-end text-end" t-out="line_price"/>
                                                        </div>
                                                    </div>
                                                    <t t-if="record.discount?.raw_value">
                                                        <div class="row">
                                                            <div class="col-12 text-muted">
                                                                Remise :
                                                                <t t-out="record.discount.value"/>%
                                                            </div>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </t>
                                        <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                            <div class="row">
                                                <div class="col-12">
                                                    <t t-out="record.name.value"/>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </t>
                            </templates>
                        </kanban>
                    </field>
                    <div class="float-end d-flex gap-1 mb-2 ms-1" name="so_button_below_order_lines">
                    </div>
                    <group name="note_group" col="6" class="mt-2 mt-md-0">
                        <group colspan="4">
                            <field colspan="2" name="note" nolabel="1" placeholder="Conditions générales..."
                                   field_id="note_0"/>
                        </group>
                        <group class="oe_subtotal_footer" colspan="2" name="sale_total">
                            <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2"
                                   readonly="1" field_id="tax_totals_0"/>
                        </group>
                        <div class="clearfix"/>
                    </group>
                </page>
            </page>


        </field>
    </record>
</odoo>
